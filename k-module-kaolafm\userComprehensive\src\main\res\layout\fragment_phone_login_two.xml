<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/phone_root_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fitsSystemWindows="true"
    tools:background="@drawable/bg_home">

    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/m500"
        android:layout_marginTop="@dimen/m73"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/scrollView_cl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/phone_tip"
                android:layout_width="@dimen/m710"
                android:layout_height="wrap_content"
                android:text="@string/user_login_phone_tip"
                android:textColor="@color/mine_login_tips_text_color"
                android:textSize="@dimen/text_size4"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <EditText
                android:id="@+id/et_input_num"
                android:layout_width="@dimen/m710"
                android:layout_height="@dimen/m76"
                android:layout_marginTop="@dimen/m20"
                android:background="@drawable/user_login_input_bg"
                android:hint="@string/user_input_phone_num"
                android:imeOptions="actionNext"
                android:inputType="number"
                android:maxLength="11"
                android:maxLines="1"
                android:nextFocusForward="@+id/et_input_code"
                android:paddingStart="@dimen/x34"
                android:textColor="@color/mine_login_tips_text_color"
                android:textColorHint="@color/mine_login_hint_text_color"
                android:textCursorDrawable="@drawable/cursor_drawable"
                android:textSize="@dimen/text_size3"
                app:layout_constraintLeft_toLeftOf="@+id/phone_tip"
                app:layout_constraintTop_toBottomOf="@+id/phone_tip" />

            <EditText
                android:id="@+id/et_input_code"
                android:layout_width="@dimen/m340"
                android:layout_height="@dimen/m76"
                android:layout_marginTop="@dimen/m16"
                android:background="@drawable/user_login_input_bg"
                android:hint="@string/user_verification_code"
                android:inputType="number"
                android:maxLength="6"
                android:maxLines="1"
                android:paddingStart="@dimen/x34"
                android:textColor="@color/mine_login_tips_text_color"
                android:textColorHint="@color/mine_login_hint_text_color"
                android:textCursorDrawable="@drawable/cursor_drawable"
                android:textSize="@dimen/text_size3"
                app:layout_constraintLeft_toLeftOf="@id/et_input_num"
                app:layout_constraintTop_toBottomOf="@id/et_input_num" />

            <Button
                android:id="@+id/btn_get_code"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="0dp"
                android:layout_height="@dimen/m76"
                android:layout_marginStart="@dimen/m60"
                android:layout_marginTop="@dimen/m16"
                android:background="@drawable/selector_code_btn_bg"
                android:text="@string/user_get_verification_code"
                android:textAllCaps="false"
                android:textColor="@color/mine_login_tips_text_color"
                android:textSize="@dimen/text_size3"
                app:layout_constraintBottom_toBottomOf="@id/et_input_code"
                app:layout_constraintLeft_toRightOf="@id/et_input_code"
                app:layout_constraintRight_toRightOf="@id/et_input_num" />

            <Button
                android:id="@+id/btn_login"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="@dimen/m710"
                android:layout_height="@dimen/m76"
                android:layout_marginTop="@dimen/m36"
                android:background="@drawable/bg_login_btn"
                android:text="@string/user_login"
                android:textColor="@color/mine_login_tips_text_color"
                android:textSize="@dimen/text_size4"
                app:layout_constraintLeft_toLeftOf="@+id/phone_tip"
                app:layout_constraintTop_toBottomOf="@id/btn_get_code" />

            <TextView
                android:id="@+id/tv_no_login_ps"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/m16"
                android:text="@string/user_notice_string"
                android:textColor="@color/mine_login_hint_text_color"
                android:textSize="@dimen/text_size3"
                app:layout_constraintLeft_toLeftOf="@id/et_input_code"
                app:layout_constraintTop_toBottomOf="@id/btn_login" />

            <View
                android:layout_width="0dp"
                android:layout_height="@dimen/m250"
                app:layout_constraintEnd_toEndOf="@+id/tv_no_login_ps"
                app:layout_constraintStart_toStartOf="@+id/tv_no_login_ps"
                app:layout_constraintTop_toBottomOf="@+id/tv_no_login_ps" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>