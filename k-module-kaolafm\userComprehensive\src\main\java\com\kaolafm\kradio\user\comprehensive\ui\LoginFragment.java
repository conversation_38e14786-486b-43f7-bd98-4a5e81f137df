package com.kaolafm.kradio.user.comprehensive.ui;

import android.content.Context;
import android.content.res.Configuration;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Handler;
import android.provider.Settings;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import com.kaolafm.kradio.user.comprehensive.InputDiaplayInter;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioHideSoftInputInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioSinglePhoneNumGetSmsInter;
import com.kaolafm.kradio.lib.base.flavor.LoginFragmentInter;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.TimerUtil;
import com.kaolafm.kradio.user.ui.ILoginView;
import com.kaolafm.kradio.user.ui.LoginPresenter;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

public class LoginFragment extends BaseFragment<LoginPresenter> implements ILoginView {

    ConstraintLayout mRootLayout;
    TextView mPhoneTip;
    Button btnGetCode;
    EditText mEtPhoneNum;
    EditText mEtVerificationCode;
    Button loginButton;

    private InputMethodManager mInputManager;
    private Handler mMainHandler;
    private ContentObserver mCloseScreenObserver;
    private String TAG = "LoginFragment";

    LoginFragmentInter loginFragmentInter;

    /**
     * true登录成功。
     */
    private boolean mLoginSuccess = false;

    private TimerUtil mTimerUtil;

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_phone_login;
    }

    @Override
    protected int getLayoutId_Tow() {
        return R.layout.fragment_phone_login_two;
    }

    @Override
    protected LoginPresenter createPresenter() {
        return new LoginPresenter(this);
    }

    KRadioHideSoftInputInter mHideSoftInputInter;

    @Override
    public void initView(View view) {
        mRootLayout= view.findViewById(R.id.phone_root_layout);
        mPhoneTip= view.findViewById(R.id.phone_tip);
        btnGetCode= view.findViewById(R.id.btn_get_code);
        mEtPhoneNum= view.findViewById(R.id.et_input_num);
        mEtVerificationCode= view.findViewById(R.id.et_input_code);
        loginButton= view.findViewById(R.id.btn_login);
        btnGetCode.setOnClickListener(this::onViewClicked);
        loginButton.setOnClickListener(this::onViewClicked);
        mEtPhoneNum.setOnClickListener(this::onViewClicked);
        mEtVerificationCode.setOnClickListener(this::onViewClicked);

        mHideSoftInputInter = ClazzImplUtil.getInter("KRadioHideSoftInputImpl");

        InputDiaplayInter mInputDiaplayInter = ClazzImplUtil.getInter("InputDiaplayImpl");
        if (mInputDiaplayInter != null && mInputDiaplayInter.isCustomDisplay()) {
            mMainHandler = new Handler();
            mCloseScreenObserver = new ContentObserver(mMainHandler) {
                @Override
                public void onChange(boolean selfChange, Uri uri) {
                    super.onChange(selfChange, uri);
                    Log.i(TAG, "onChange: uri = " + uri);
                    Context ctx = getContext();
                    if (ctx != null) {
                        //get state of close screen window
                        int displayState = Settings.System.getInt(ctx.getContentResolver(), "DISP_STATE", 1);
                        Log.i(TAG, "onChange: get DISPLAY_STATE = " + displayState);
                        if (displayState == 0) {
                            destroyInputMethod(mEtPhoneNum);
                            destroyInputMethod(mEtVerificationCode);
                        }
                    } else {
                        Log.i(TAG, "onChange: get context is null");
                    }
                }
            };
            getActivity().getContentResolver().registerContentObserver(Settings.System.CONTENT_URI, true, mCloseScreenObserver);
        } else {
            getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
        }
        loginFragmentInter = ClazzImplUtil.getInter("LoginFragmentImpl");
        if (loginFragmentInter != null) {
            loginFragmentInter.initView(getActivity(), mEtPhoneNum, mEtVerificationCode);
        }
        if (mRootLayout != null) {
            //亿咖通EASODP-10754云听-账号登陆界面登陆完成后键盘无法隐藏 通用布局添加了一个id
            mRootLayout.setOnTouchListener((v, event) -> {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    hideSoftInput();
                }
                return false;
            });
        }
        mEtVerificationCode.setOnKeyListener(onKeyListener);
    }

    View.OnKeyListener onKeyListener = new View.OnKeyListener() {
        @Override
        public boolean onKey(View v, int keyCode, KeyEvent event) {
            Log.i(TAG, "onKeyListener keyCode:" + keyCode);
            switch (keyCode) {
                case KeyEvent.KEYCODE_ENTER:
                case EditorInfo.IME_ACTION_DONE:
                    hideSoftInput();
                    return true;
            }
            return false;
        }
    };

    private void createInputMethod(View view) {
        if (view != null && mInputManager == null) {
            mInputManager = (InputMethodManager) view.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
            mInputManager.showSoftInput(view, InputMethodManager.SHOW_FORCED);
        }
    }


    private void destroyInputMethod(View view) {
        try {
            if (mInputManager != null && view != null) {
                Log.i(TAG, "destroyInputMethod");
                mInputManager.hideSoftInputFromWindow(view.getWindowToken(), 0);
            } else {
                Log.i(TAG, "destroyInputMethod: out of bound");
            }
        } catch (Exception e) {
            Log.i(TAG, "destroyInputMethod: exception = " + e.getMessage());
            e.printStackTrace();
        } finally {
            mInputManager = null;
        }
    }

    public void onViewClicked(View view) {
        int id = view.getId();
        if (!AntiShake.check(id)) {
            if (isNetworkUnavailable()) {
                return;
            }
            if (id == R.id.btn_get_code) {
                getVerificationCode();
                ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, ButtonExposureOrClickReportEvent.BUTTON_ID_SEND_VERIFICATION_CODE, btnGetCode.getText().toString(), ReportParameterManager.getInstance().getPage(), ReportConstants.CONTROL_TYPE_SCREEN));
            } else if (id == R.id.btn_login) {
                setViewEnable(loginButton, false);
                hasHideInput();
                if (!mLoginSuccess) {
                    mPresenter.login(mEtPhoneNum.getText().toString(), mEtVerificationCode.getText().toString());
                }
            } else if (id == R.id.et_input_code) {
                createInputMethod(mEtVerificationCode);
            } else if (id == R.id.et_input_num) {
                createInputMethod(mEtPhoneNum);
            }
        }
    }

    private void hasHideInput() {//账号登陆界面登陆完成后键盘无法隐藏
        if (mRootLayout != null) {
            destroyInputMethod(mRootLayout);
        }
    }

    private boolean isCanHideInput() {
        return null != mHideSoftInputInter && mHideSoftInputInter.hasHideSoftInput();
    }

    private boolean isNetworkUnavailable() {
        if (!NetworkUtil.isNetworkAvailable(getContext(), false)) {
            toast(R.string.no_net_work_str);
            return true;
        }
        return false;
    }

    private void getVerificationCode() {
        if (mTimerUtil != null && mTimerUtil.isCountDownInProgress()) {
            KRadioSinglePhoneNumGetSmsInter inter = ClazzImplUtil.getInter("KRadioSinglePhoneNumGetSmsImpl");
            if (inter != null && inter.isOnlyOnePhoneNumGetSms()) {
                toast("1分钟内最多获得一次验证码");
                return;
            }
        }
        mPresenter.getVerificationCode(mEtPhoneNum.getText().toString().trim());
    }

    @Override
    public void failedToGetCode(ApiException e) {
        enableButton();
        toast(e.getMessage());
    }

    @Override
    public void startCountdown() {
        btnGetCode.setEnabled(false);
        btnGetCode.setBackground(ResUtil.getDrawable(R.drawable.selector_code_btn_not_bg));
        mTimerUtil = TimerUtil.newInstance();
        mTimerUtil.countdown(60, num -> {
            if (btnGetCode != null) {
                btnGetCode.setText("重新发送(" + num + "s)");
            }
        }, this::enableButton);
    }

    /**
     * 使获取验证码按钮恢复可点击
     */
    private void enableButton() {
        if (btnGetCode != null) {
            cancelCountdown();
            btnGetCode.setBackground(ResUtil.getDrawable(R.drawable.selector_code_btn_bg));
            btnGetCode.setEnabled(true);
            btnGetCode.setText(R.string.user_get_verification_code);
        }
    }

    /**
     * 重置登录UI
     */
    public void resetView() {
        mEtVerificationCode.setText("");
        mEtPhoneNum.setText("");
        enableButton();
        setViewEnable(loginButton, true);
    }


    @Override
    public void loginSuccess() {
//        UserLoginFragment fragment = (UserLoginFragment)getParentFragment();
//        if (fragment != null) {
//            fragment.showLoginSuccessToast();
//            fragment.getUserInfo();
//        }
//        hasHideInput();
//        ((UserLoginFragment) getParentFragment()).getUserInfo();
//        mLoginSuccess = true;
        UserLoginActivity fragment = (UserLoginActivity) getActivity();
        if (fragment != null) {
            if (fragment != null) {
                fragment.showLoginSuccessToast();
                fragment.getUserInfo();
            }
            hasHideInput();
            mLoginSuccess = true;
        }
    }

    @Override
    public void loginError() {
        setViewEnable(loginButton, true);
    }


    private void setViewEnable(View view, boolean enable) {
        if (view == null) {
            return;
        }
        view.setEnabled(enable);
    }

    @Override
    public void onSupportInvisible() {
        super.onSupportInvisible();
        if (mLoginSuccess) {
            resetView();
        }
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);

        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            uploadView(true);
        } else {
            uploadView(false);
        }
    }

    private void uploadView(boolean isLand) {
        ConstraintSet set = new ConstraintSet();
        set.clone(mRootLayout);
        if (isLand) {
            set.setHorizontalBias(mPhoneTip.getId(), 0.4f);
        } else {
            set.setHorizontalBias(mPhoneTip.getId(), 0.5f);
        }
        set.applyTo(mRootLayout);
    }

    @Override
    public void toast(String msg) {
        ToastUtil.showOnActivity(getContext(), msg);
    }

    @Override
    public void onPause() {
        if (loginFragmentInter != null) {
            loginFragmentInter.onPause();
        }
        super.onPause();
        destroyInputMethod(mRootLayout);
    }

    @Override
    public void onResume() {
        super.onResume();
        if (loginFragmentInter != null) {
            loginFragmentInter.onResume();
        }

        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_SEND_VERIFICATION_CODE, btnGetCode.getText().toString(), ReportParameterManager.getInstance().getPage(), ReportConstants.CONTROL_TYPE_SCREEN));
    }

    @Override
    public void onDestroy() {
        if (mCloseScreenObserver != null) {
            getActivity().getContentResolver().unregisterContentObserver(mCloseScreenObserver);
        }
        if (mMainHandler != null) {
            mMainHandler.removeCallbacksAndMessages(null);
        }
        mCloseScreenObserver = null;
        mMainHandler = null;
        destroyInputMethod(mEtPhoneNum);
        destroyInputMethod(mEtVerificationCode);

        if (loginFragmentInter != null) {
            loginFragmentInter.onDestroy();
        }
        super.onDestroy();
        cancelCountdown();
    }

    private void cancelCountdown() {
        if (mTimerUtil != null) {
            mTimerUtil.cancel();
            mTimerUtil = null;
        }
    }

    private void toast(int resId) {
        toast(ResUtil.getString(resId));
    }

    @Override
    protected void changeViewLayoutForStatusBar(View view) {
    }

    @Override
    protected void addFragmentRootViewPadding(View view) {
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (!isVisibleToUser) {
            hasHideInput();
        }

    }
}
